package com.mercaso.ims.infrastructure.util;

import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class DateUtils {

    private DateUtils() {
    }

    //2021-03-24T00:00:00Z
    public static Instant getStartOfCurrentDay(Instant instant) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return startOfDay.toInstant(ZoneOffset.UTC);
    }

    public static Instant fromTimestamp(Timestamp date) {
        if (date != null) {
            return date.toInstant();
        }

        return null;
    }

    public static Instant fromStringTime(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        Timestamp timestamp = Timestamp.valueOf(date);
        return fromTimestamp(timestamp);
    }


    /**
     * Parse date string supporting multiple formats: MM/dd/yyyy, yyyy/MM/dd, and yyyy-MM-dd
     * Returns the start of day (00:00:00) in UTC timezone as an Instant
     */
    public static Instant parseDate(String dateStr, String timeZone) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // List of supported date formats in order of preference
        DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("MM/dd/yyyy"),  // 09/10/2025
                DateTimeFormatter.ofPattern("yyyy/MM/dd"),  // 2025/09/10
                DateTimeFormatter.ISO_LOCAL_DATE            // 2025-09-10
        };

        // Try each format until one succeeds
        for (DateTimeFormatter formatter : formatters) {
            try {
                LocalDate localDate = LocalDate.parse(dateStr, formatter);
                ZoneId zone = StringUtils.isBlank(timeZone) ? ZoneOffset.UTC : ZoneId.of(timeZone);

                // Convert LocalDate to Instant at start of day in UTC
                return localDate.atStartOfDay(zone).toInstant();
            } catch (DateTimeException e) {
                // Continue to next format
                log.error("Error parsing date: {}", e.getMessage());
            }
        }

        // If all formats fail, throw exception with helpful message
        throw new DateTimeParseException(
                "Unable to parse date: " + dateStr + ". Supported formats: MM/dd/yyyy, yyyy/MM/dd, yyyy-MM-dd",
                dateStr, 0);
    }

    /**
     * Parse end date string supporting multiple formats: MM/dd/yyyy, yyyy/MM/dd, and yyyy-MM-dd
     * Returns the end of day (23:59:59.999999999) in UTC timezone as an Instant
     */
    public static Instant parseEndDate(String dateStr, String timeZone) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // List of supported date formats in order of preference
        DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("MM/dd/yyyy"),  // 09/10/2025
                DateTimeFormatter.ofPattern("yyyy/MM/dd"),  // 2025/09/10
                DateTimeFormatter.ISO_LOCAL_DATE            // 2025-09-10
        };

        // Try each format until one succeeds
        for (DateTimeFormatter formatter : formatters) {
            try {
                LocalDate localDate = LocalDate.parse(dateStr, formatter);
                ZoneId zone = StringUtils.isBlank(timeZone) ? ZoneOffset.UTC : ZoneId.of(timeZone);
                // Convert LocalDate to Instant at end of day in UTC (23:59:59.999999999)
                return localDate.atTime(LocalTime.MAX).atZone(zone).toInstant();
            } catch (DateTimeException e) {
                // Continue to next format
                log.error("Error parsing end date: {}", e.getMessage());
            }
        }

        // If all formats fail, throw exception with helpful message
        throw new DateTimeParseException(
                "Unable to parse end date: " + dateStr + ". Supported formats: MM/dd/yyyy, yyyy/MM/dd, yyyy-MM-dd",
                dateStr, 0);
    }


}
